# ASN Geographical Distribution Analyzer

## 中文说明

实现一个自治域地理分布查询程序，支持两种分析模式：

### 功能特性
* 用户输入自治域号
* 使用BGPView查询自治域信息，获取宣告的IPv4前缀数据（示例：https://bgpview.io/asn/138915#prefixes-v4）
* **两种分析模式**：
  - **C段模式**（默认）：将前缀拆解为C段（/24子网）进行分析
  - **IP模式**：逐个IP地址进行分析
* 使用Maxmind Geoip2数据库查询每个C段或IP的国家、省份、城市（数据库文件为GeoLite2-City.mmdb）
* 将结果保存到CSV表格
* 输出该自治域IP的国家分布和占比

### 使用方法

```bash
# C段模式分析（默认）
python asn_geo_analyzer.py 138915

# IP模式分析
python asn_geo_analyzer.py 138915 --by-ip

# 指定输出文件
python asn_geo_analyzer.py 138915 -o custom_output.csv

# 指定GeoIP数据库路径
python asn_geo_analyzer.py 138915 -d /path/to/GeoLite2-City.mmdb
```

---

## English Description

Create a Python program that analyzes the geographical distribution of IP addresses for a given Autonomous System (AS) with two analysis modes.

### Features

**Input Requirements:**
- Accept an AS number (ASN) as user input (e.g., 138915)

**Data Collection:**
- Query the BGPView API to retrieve IPv4 prefix announcements for the specified ASN
  - Website: https://api.bgpview.io/asn/{asn}
  - Example for ASN 138915: https://bgpview.io/asn/138915#prefixes-v4
- Extract all announced IPv4 prefixes from the API response

**IP Address Processing (Two Modes):**
1. **Subnet Mode (Default)**: Break down each IPv4 prefix into /24 subnets (Class C networks)
2. **IP Mode**: Extract and analyze individual IP addresses from prefixes

**Geolocation Analysis:**
- Use the MaxMind GeoLite2 database (GeoLite2-City.mmdb file) to determine geographical information
- For each subnet/IP, retrieve: country, state/province, and city
- Use the GeoIP2-python library (reference: https://github.com/maxmind/GeoIP2-python)
- Handle cases where geolocation data may be unavailable

**Output Requirements:**
1. **CSV File**: Create a detailed CSV file with columns:
   - Subnet/IP, Country, State/Province, City
   - One row per /24 subnet or individual IP

2. **Summary Report**: Display statistics showing:
   - Country-wise distribution
   - Percentage breakdown by country
   - Total number of items analyzed

### Usage

```bash
# Subnet mode analysis (default)
python asn_geo_analyzer.py 138915

# Individual IP analysis mode
python asn_geo_analyzer.py 138915 --by-ip

# Custom output filename
python asn_geo_analyzer.py 138915 -o custom_output.csv

# Custom GeoIP database path
python asn_geo_analyzer.py 138915 -d /path/to/GeoLite2-City.mmdb
```

### Installation

```bash
pip install -r requirements.txt
```

### Files

- `asn_geo_analyzer.py` - Main analyzer program
- `test_analyzer.py` - Test script demonstrating both modes
- `requirements.txt` - Python dependencies
- `GeoLite2-City.mmdb` - MaxMind GeoIP database (required)

**Technical Specifications:**
- Implement proper error handling for API failures and missing geolocation data
- Use appropriate Python libraries (requests for API calls, geoip2 for database queries, csv for file output)
- Ensure the program can handle large ASNs with many prefixes efficiently
- Include progress indicators for long-running operations
- Support both subnet-level and IP-level analysis modes