#!/usr/bin/env python3
"""
Test script for the ASN Geographical Analyzer

This script demonstrates both analysis modes:
1. By /24 subnets (default)
2. By individual IP addresses (--by-ip)
"""

from asn_geo_analyzer import ASNGeoAnalyzer


def test_subnet_mode():
    """Test analysis by /24 subnets."""
    print("="*60)
    print("Testing Subnet Mode (/24 analysis)")
    print("="*60)
    
    analyzer = ASNGeoAnalyzer("GeoLite2-City.mmdb", analyze_by_ip=False)
    
    # Use a smaller ASN for testing
    test_asn = 13335  # Cloudflare - should have manageable number of prefixes
    
    try:
        analyzer.analyze_asn(test_asn)
        print("Subnet mode test completed successfully!")
    except Exception as e:
        print(f"Error in subnet mode test: {e}")


def test_ip_mode():
    """Test analysis by individual IP addresses."""
    print("\n" + "="*60)
    print("Testing IP Mode (individual IP analysis)")
    print("="*60)
    
    analyzer = ASNGeoAnalyzer("GeoLite2-City.mmdb", analyze_by_ip=True)
    
    # Use a very small ASN for IP testing to avoid memory issues
    test_asn = 138915  # Should be smaller
    
    try:
        analyzer.analyze_asn(test_asn)
        print("IP mode test completed successfully!")
    except Exception as e:
        print(f"Error in IP mode test: {e}")


def main():
    """Run both test modes."""
    print("ASN Geographical Analyzer - Test Script")
    print("This script will test both analysis modes with sample ASNs")
    
    # Test subnet mode first (faster)
    test_subnet_mode()
    
    # Ask user if they want to test IP mode (can be slow for large ASNs)
    response = input("\nDo you want to test IP mode? This may take longer for large ASNs (y/n): ").lower().strip()
    if response == 'y':
        test_ip_mode()
    else:
        print("Skipping IP mode test.")
    
    print("\nTest script completed!")


if __name__ == "__main__":
    main()
