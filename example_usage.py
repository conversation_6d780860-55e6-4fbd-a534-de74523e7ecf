#!/usr/bin/env python3
"""
Example usage of the ASN Geographical Analyzer

This script demonstrates how to use the ASNGeoAnalyzer class programmatically.
"""

from asn_geo_analyzer import ASNGeoAnalyzer


def main():
    """Example usage of the ASN analyzer."""
    
    # Initialize the analyzer
    analyzer = ASNGeoAnalyzer("GeoLite2-City.mmdb")
    
    # Example ASN numbers to analyze
    example_asns = [
        138915,  # Example from the requirements
        13335,   # Cloudflare
        15169,   # Google
    ]
    
    print("ASN Geographical Distribution Analyzer - Example Usage")
    print("=" * 60)
    
    for asn in example_asns:
        print(f"\nAnalyzing ASN {asn}...")
        try:
            analyzer.analyze_asn(asn)
            print(f"Analysis complete for ASN {asn}")
        except Exception as e:
            print(f"Error analyzing ASN {asn}: {e}")
        
        print("-" * 40)


if __name__ == "__main__":
    main()
